<html xmlns="http://www.w3.org/1999/xhtml" lang="ja" xml:lang="ja"><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<meta http-equiv="Content-Script-Type" content="text/javascript">
<title>EN-DANCE SYSTEM</title>
<link href="css/import.css" rel="stylesheet" type="text/css" media="all">
<link href="css/jquery-ui.css" rel="stylesheet" type="text/css" media="all">
<script type="text/javascript" charset="UTF-8" src="https://www.en-system.net/staff/tool/js/input_check.js"></script>
<script type="text/javascript" src="js/jquery-1.8.3.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<script type="text/javascript" src="js/jquery.ui.datepicker-ja.min.js"></script>


<style type="text/css">
.SideNavi li#sideNav6 a {
	background-color: #6D6B6A;color:#DADF00;
}
#wrapMain table.newsTbl th{
    font-size:90%;
    padding: 5px 2px;
}
span.orsort{
	float:right;
	font-size:90%;
}
span.orsort a{
	font-size:85%;
}
.sir_kbn_1, .sir_kbn_2, .sir_kbn_3, .sir_kbn_4, .sir_kbn_5{
	display: none;
}
.course-biko{
	margin: 3px 5px;
	width: 80px;
}
</style>
<script language="JavaScript">
<!--
$(function () {
	var kbn = "1";
	if(kbn == ""){
		kbn = 1;
	}
	$("[class^='sir_kbn_']").find('input, select, textarea').prop('disabled', true);
	$('.sir_kbn_' + kbn).show();
	$('.sir_kbn_' + kbn).find('input, select, textarea').prop('disabled', false);
});

$(document).on('change', '#sir_kbn', function(){
	$("[class^='sir_kbn_']").hide();
	$("[class^='sir_kbn_']").find('input, select, textarea').prop('disabled', true);
	$('.sir_kbn_' + $(this).val()).show();
	$('.sir_kbn_' + $(this).val()).find('input, select, textarea').prop('disabled', false);
});

function refNum(y,m,d) {
	if( y.value != '' && m.value != '' && d.value != '' ) {
		if( DateCheck_1( y.value+'-'+m.value+'-'+d.value ) == false ) {
			alert("日付が正しくありません");
			y.focus();
			return false;
		}
	}
	return true;
}

function refNumPost(y,m,d) {
	if( y.value != '' && m.value != '' && d.value != '' ) {
		if( DateCheck_1( y.value+'-'+m.value+'-'+d.value ) == false ) {
			alert("日付が正しくありません");
			y.focus();
			return false;
		}
	}else if( y.value != '' || m.value != '' || d.value != '' ){
		alert("日付が正しくありません");
		y.focus();
		return false;
	}
	return true;
}

function refNengetu(y,m) {
	if( (y.value != '' && m.value == '') || (y.value == '' && m.value != '') ){
		alert("年月が正しくありません");
		y.focus();
		return false;
	}
	return true;
}

function InputCheck(parts){
	if( parts.sir_kbn.value == 1 ){
		y = parts.sir_nen_ta_k1;
		m = parts.sir_tuki_ta_k1;
		if( refNengetu( y,m ) == false ) {
			return false;
		}

		y = parts.sir_hiki_k1_year;
		m = parts.sir_hiki_k1_month;
		d = parts.sir_hiki_k1_day;
		if( refNumPost( y,m,d ) == false ) {
			return false;
		}

		y = parts.sir_nen_ge_k1;
		m = parts.sir_tuki_ge_k1;
		if( refNengetu( y,m ) == false ) {
			return false;
		}
	}

	if( parts.sir_kbn.value == 2 ){
		y = parts.sir_st_k2_year;
		m = parts.sir_st_k2_month;
		d = parts.sir_st_k2_day;
		if( refNumPost( y,m,d ) == false ) {
			return false;
		}
	}

	if( parts.sir_kbn.value == 3 ){
		y = parts.sir_nen_ta_k3;
		m = parts.sir_tuki_ta_k3;
		if( refNengetu( y,m ) == false ) {
			return false;
		}

		y = parts.sir_nen_re_k3;
		m = parts.sir_tuki_re_k3;
		if( refNengetu( y,m ) == false ) {
			return false;
		}
	}

	if( parts.sir_kbn.value == 4 ){
		y = parts.sir_nen_ta_k4;
		m = parts.sir_tuki_ta_k4;
		if( refNengetu( y,m ) == false ) {
			return false;
		}

		y = parts.sir_hiki_k4_year;
		m = parts.sir_hiki_k4_month;
		d = parts.sir_hiki_k4_day;
		if( refNumPost( y,m,d ) == false ) {
			return false;
		}

		y = parts.sir_nen_ge_k4;
		m = parts.sir_tuki_ge_k4;
		if( refNengetu( y,m ) == false ) {
			return false;
		}
	}

	if( parts.sir_kbn.value == 5 ){
		y = parts.sir_nen_st_k5;
		m = parts.sir_tuki_st_k5;
		if( refNengetu( y,m ) == false ) {
			return false;
		}
	}
	
	ret_com = confirm("登録します。よろしいですか？");
	if( !ret_com ){
		return false;
	}
	parts.submit();
	return true;
}

DeleteCheck
function DeleteCheck(parts){

	
	ret_com = confirm("削除します。よろしいですか？");
	if( !ret_com ){
		return false;
	}
	parts.commit_mode.value="delete";
	parts.submit();
	return true;
}


$(function () {
	$("input:radio[name=kbn]").change(function() {
		if($(this).val() == '9'){
			$('#po_limit_st').prop("disabled", true);
			$('#po_limit_st').val('');
			$('#po_limit_ed').prop("disabled", true);
			$('#po_limit_ed').val('');
			$('#s_minus').text('-');
			$('#po_kbn').val('');
			$('#po_kbn').prop("disabled", true);
			$('#pl_id').val('');
			$('#pl_id').prop("disabled", true);
		}else{
			$('#po_limit_st').prop("disabled", false);
			$('#po_limit_ed').prop("disabled", false);
			$('#s_minus').text('');
			$('#po_kbn').prop("disabled", false);
			$('#pl_id').prop("disabled", false);
		}
	});

    $("#po_kbn").change(function() {
        $.ajax({
            type: "post",
            url: "ajax_get_point_list.php",
            data: {po_kbn: $(this).val()}
        }).done(function(data){
            $('#select_pl_id').html(data);
			$('#po_limit_st').val('');
			$('#po_limit_ed').val('');
			$('#po_give_point').val('');
        }).fail(function(XMLHttpRequest, textStatus, errorThrown){
            alert(errorThrown);
        });
    });

	$(document).on('change', '#pl_id', function(){
		$('#po_name').val($(this).find(':selected').attr('data-nm'));
		$('#po_limit_st').val($(this).find(':selected').attr('data-st'));
		$('#po_limit_ed').val($(this).find(':selected').attr('data-ed'));
		$('#po_give_point').val($(this).find(':selected').attr('data-po'));
	});

});

//-->
</script>
</head>

<body>
<!-- #wrapAll -->

<div id="wrapAll">


<!-- heade.tpl -->

<!-- #topH1  -->
<div id="topH1">
<div class="boxHead">
<a href="./index.php"><img src="../images/logo.jpg" height="50"></a>
</div>
<p id="nameP">2025/05/22（木）</p>
<h2>スタッフ用ツール　　　
<select name="Studio" id="selStudios" onchange="document.move_room.room_id.value=this.options[this.selectedIndex].value;document.move_room.submit();">
  <option value="1" selected="">渋谷校</option>
  <option value="9">渋谷2nd校</option>
  <option value="10">渋谷SCRAMBLE</option>
  <option value="11">EnSTUDIO</option>
  <option value="2">横浜校</option>
</select>
　　平野さん</h2>

<div class="div_tab clearfix mb05">
<span class="btntab1">メインメニュー</span>
<span class="btntab2"><a href="rental_yoyaku_main.php">貸しスタジオ</a></span>
</div>

<ul>
  <li id="btnLogout"><a href="../logout.php">ログアウト</a></li>
</ul>

<form action="move_room.php" name="move_room" method="post">
<input type="hidden" name="room_id" value="">
<input type="hidden" name="login_mode" value="login">
</form>

</div>
<!--/ #topH1 -->

<!-- #glnavi -->
<!--/ #glnavi -->

<!-- /heade.tpl -->


<div class="bread">
</div>
<!--/ .bread -->

<!-- #wrapMain -->
<div id="wrapMain" class="clearfix">

<!-- #contentR-->
<div id="content" class="clearfix">

<h2><span>会員署名詳細</span></h2>
<div>
<table summary="登録・編集" class="IchiranTbl2" style="margin-bottom:10px;">
<colgroup class="td05"></colgroup>
<colgroup class="td10"></colgroup>
<colgroup class="td05"></colgroup>
<colgroup class="td10"></colgroup>
<colgroup class="td05"></colgroup>
<colgroup class="td10"></colgroup>
<tbody><tr>
	<th scope="col" class="thLeft">入会受付日</th>
	<td scope="col">2022-09-04</td>
	<th scope="col" class="thLeft">会員番号</th>
	<td scope="col">S0007577</td>
	<th scope="col" class="thLeft">在籍状態</th>
	<td scope="col">
退会（2024-04-30）	</td>
</tr>
<tr>
    <th scope="col" class="thLeft">氏名</th>
    <td scope="col">安藤  優</td>
    <th scope="col" class="thLeft">フリガナ</th>
    <td scope="col">アンドウ  ユウ    （男性）</td>
    <th class="thLeft">生年月日</th>
    <td>1998-10-23（26歳）    </td>
</tr>
</tbody></table>
</div>



    <div>

<h3 id="newregist">詳細</h3>
<form name="edit_for2m" action="user_sign_mnt.php" method="post">
<table summary="登録・編集" class="IchiranTbl2" style="margin-bottom:10px;">
    <colgroup class="td03"></colgroup>
	<colgroup class="td07"></colgroup>

    <colgroup class="td10"></colgroup>

    <colgroup class="td03"></colgroup>
        <tbody><tr>
 
			<th scope="col">区分</th>
			<th scope="col">マイページ</th>
			
		</tr>
	            <tr><td class="txtC">
				退会            </td>
            <td class="txtC">
				表示する			</td>	
		</tr>

		<!-- 退会 -->
		<tr class="sir_kbn_1" style="display: table-row;">
			<th scope="col">担当者</th>
			<th scope="col">退会年月日</th>
		</tr>
		<tr class="sir_kbn_1" style="display: table-row;">
			<td class="txtC">
				堀江優菜			</td>
			<td class="txtC">
				2024年 04月 末日			</td>
		</tr>
		<tr class="sir_kbn_1" style="display: table-row;">
			<th scope="col">最終引落日</th>
			<th scope="col">最終月謝</th>
		</tr>
		<tr class="sir_kbn_1" style="display: table-row;">
			<td class="txtC">
				2024年 03月 27日			</td>
			<td class="txtC">
				2024年 04月分			</td>
		</tr>

		<!-- コース変更 -->
		<tr class="sir_kbn_2">
			<th scope="col">担当者</th>
			<th scope="col">適用開始日</th>
		</tr>
		<tr class="sir_kbn_2">
			<td class="txtC">
				堀江優菜			</td>
			<td class="txtC">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_2">
			<th scope="col" colspan="2">現在在籍コース</th>
		</tr>
		<tr class="sir_kbn_2">
			<td class="txtL" colspan="2">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_2">
			<th scope="col" colspan="2">変更先コース</th>
		</tr>
		<tr class="sir_kbn_2">
			<td class="txtL" colspan="2">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_2">
			<th scope="col">適用期間</th>
			<th scope="col">変更先金額</th>
		</tr>
		<tr class="sir_kbn_2">
			<td class="txtC">
				&nbsp;			</td>
			<td class="txtC">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_2">
			<th scope="col" colspan="2">コース変更差額お支払金額</th>
		</tr>
		<tr class="sir_kbn_2">
			<td class="txtC" colspan="2">
				&nbsp;			</td>
		</tr>

		<!-- 休会 -->
		<tr class="sir_kbn_3">
			<th scope="col">担当者</th>
			<th scope="col">退会年月日</th>
		</tr>
		<tr class="sir_kbn_3">
			<td class="txtC">
				堀江優菜			</td>
			<td class="txtC">
				2024年 04月 末日			</td>
		</tr>
		<tr class="sir_kbn_3">
			<th scope="col" colspan="2">利用再開始日</th>
		</tr>
		<tr class="sir_kbn_3">
			<td class="txtC" colspan="2">
				&nbsp;			</td>
		</tr>

		<!-- ウォータースタンド解約 -->
		<tr class="sir_kbn_4">
			<th scope="col">担当者</th>
			<th scope="col">退会年月日</th>
		</tr>
		<tr class="sir_kbn_4">
			<td class="txtC">
				堀江優菜			</td>
			<td class="txtC">
				2024年 04月 末日			</td>
		</tr>
		<tr class="sir_kbn_4">
			<th scope="col">最終引落日</th>
			<th scope="col">最終月謝</th>
		</tr>
		<tr class="sir_kbn_4">
			<td class="txtC">
				2024年 03月 27日			</td>
			<td class="txtC">
				2024年 04月分			</td>
		</tr>

		<!-- 在籍変更 -->
		<tr class="sir_kbn_5">
			<th scope="col">担当者</th>
			<th scope="col">適用開始日</th>
		</tr>
		<tr class="sir_kbn_5">
			<td class="txtC">
				堀江優菜			</td>
			<td class="txtC">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_5">
			<th scope="col" colspan="2">現在在籍校</th>
		</tr>
		<tr class="sir_kbn_5">
			<td class="txtL" colspan="2">
				&nbsp;			</td>
		</tr>
		<tr class="sir_kbn_5">
			<th scope="col" colspan="2">変更先校</th>
		</tr>
		<tr class="sir_kbn_5">
			<td class="txtL" colspan="2">
				&nbsp;			</td>
		</tr>

		<tr>
            <th scope="col" colspan="2">その他</th>


        </tr>
        <tr>


            
            <td class="txtC" colspan="2">
				<p class="txtL" style="display: inline-block;">時間が取れなくなってしまったため。</p>			</td>


            <input type="hidden" name="user_id" value="">
            <input type="hidden" name="sir_id" value="37">
<input type="hidden" name="or_sort" value="">
<input type="hidden" name="mode" value="commit">
<input type="hidden" name="commit_mode" value="edit">
        </tr>
    </tbody></table>

</form></div>






<ul class="flUl-c mb25 clearfix">
<li class="btnBluSB btnBack"><span><input type="button" value="戻る" name="back" onclick="document.backform.submit();"></span></li>
</ul>

<form name="backform" action="users_sign_main.php" method="post">
<input type="hidden" name="mode" value="">
<input type="hidden" name="or_sort" value="">
  <input type="hidden" name="user_id" value="">
</form>
<div class="gotop">
<p><a href="#">ページトップへ</a></p>
</div>
<!--/ .gotop -->

</div>
<!--/ #content -->

<!-- #sideLeft -->
<div id="sideLeft">
<!-- left_menu.tpl -->
<h3>会員管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav01" class="btm"><a href="nyukai_main.php">入会申込み</a></li>
    <li id="sideNav1" class="btm"><a href="user_mnt.php">会員新規登録</a></li>
    <li id="sideNav2" class="btm"><a href="user_main.php">会員一覧</a></li>
    <li id="sideNav3" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='1';document.frm_cardread.submit();">会員カードNo登録</a></li>
    <li id="sideNav4" class="btm"><a href="mail_haishin.php">会員メール配信</a></li>
    <li id="sideNav6" class="btm"><a href="users_sign_main.php">会員署名一覧</a></li>
  </ul>
</div>
<h3>レッスン管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav30" class="btm"><a href="jyuko_search.php">レッスン受講一覧</a></li>
    <li id="sideNav31" class="btm"><a href="taiken_search.php">体験レッスン管理</a></li>
    <li id="sideNav32" class="btm"><a href="user_oshirase_main.php">My Pageお知らせ登録</a></li>
  </ul>
</div>
<h3>月謝管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav11" class="btm"><a href="course_ichiran.php">受講可能コース一覧</a></li>
    <li id="sideNav10" class="btm"><a href="gessya_search.php">会員月謝一覧</a></li>
  </ul>
</div>
<h3>販売管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav40" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='2';document.frm_cardread.submit();">商品販売</a></li>
    <li id="sideNav43" class="btm"><a href="pos_search.php">販売履歴一覧</a></li>
    <li id="sideNav44" class="btm"><a href="kinsyu_hyo.php">金種表</a></li>
    <li id="sideNav42" class="btm"><a href="pos_settei.php">レジ設定</a></li>
    <li id="sideNav41" class="btm"><a href="shohin_add_mnt.php">商品登録</a></li>
  </ul>
</div>
<h3>売上集計</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav60" class="btm"><a href="syukei_uriage.php">全体売上集計</a></li>
    <li id="sideNav61" class="btm"><a href="syukei_course.php">コース別売上集計</a></li>
    <li id="sideNav62" class="btm"><a href="syukei_shohin.php">商品別売上集計</a></li>
    <li id="sideNav65" class="btm"><a href="syukei_studio.php">スタジオ収支進捗表</a></li>
    <li id="sideNav68" class="btm"><a href="studio_transition.php">スタジオ推移表</a></li>
    <li id="sideNav66" class="btm"><a href="syukei_uriage_yoso.php">売上要素分解表</a></li>
  </ul>
</div>
<h3>日報掲示板登録</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav70" class="btm"><a href="nippo_room.php">教室日報</a></li>
    <li id="sideNav71" class="btm"><a href="nippo_staff.php">スタッフ日報</a></li>
    <!-- <li id="sideNav72" class="btm"><a href="keijiban_main.php">掲示板</a></li> -->
    <li id="sideNav73" class="btm"><a href="nippo_front.php">フロント業務日報</a></li>
  </ul>
</div>
<h3>管理機能</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav20" class="btm"><a href="getsuji_kakutei.php">月次確定処理</a></li>
	<li id="sideNav23" class="btm"><a href="point_search.php">付与ポイント管理</a></li>
    <li id="sideNav21" class="btm"><a href="holiday_main.php">休日カレンダー</a></li>
    <li id="sideNav22" class="btm"><a href="minochk_hiduke.php">未納ﾁｪｯｸ開始日設定</a></li>
  </ul>
</div>
<form action="card_write.php" name="frm_cardread" method="post" target="_self">
<input type="hidden" name="mode" value="">
<input type="hidden" name="page_mode" value="">
</form>
<!-- /left_menu.tpl -->
</div>
<!--/ #sideLeft -->
</div>
<!--/ #wrapMain -->


<!-- footer.tpl -->
<div id="footer-space"></div><!-- footer固定用スペース -->
</div>
<!--/ #wrapAll -->

<!-- footer -->
<div id="wrapFoot">
<div id="footer">
</div>
</div>
<!--/ footer -->
<!-- /footer.tpl -->

<script type="text/javascript">
<!--
var showAdditionalButton = function (input) {
    setTimeout(function () {
        var buttonPanel = $(input)
            .datepicker("widget")
            .find(".ui-datepicker-buttonpane"),
        btn = $('<button class="ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all" type="button">クリア</button>');
        btn
            .unbind("click")
            .bind("click", function () {
                $.datepicker._clearDate(input);
            });
        btn.appendTo(buttonPanel);
    }, 1);
};
$(function() {
	$.datepicker.setDefaults( $.datepicker.regional[ "ja" ] );
	$( ".datepicker" ).datepicker({
		dateFormat: "yy-mm-dd",
		//showOn: 'both',
		showButtonPanel: true,
		firstDay: 0,
		changeMonth: true,
		changeYear: true,
		yearRange: "1930:+1",
		minDate: new Date(),
		beforeShow: showAdditionalButton,
		onChangeMonthYear: showAdditionalButton
	});
});
//-->
</script>


</body></html>