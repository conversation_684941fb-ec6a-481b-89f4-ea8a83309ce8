from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging
from config import Config
from services import groups, rooms, ages, courses, short_courses, categories, lessons, shohin_cate, shohin_vari, point_search
# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(funcName)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def login(driver, config):
    """Handle login if required"""
    if config.LOGIN_URL and config.LOGIN_CREDENTIALS:
        try:
            logger.info(f"Logging in at {config.LOGIN_URL}")
            driver.get(config.LOGIN_URL)
            driver.find_element(By.NAME, "login_id").send_keys(config.LOGIN_CREDENTIALS["username"])
            driver.find_element(By.NAME, "login_pass").send_keys(config.LOGIN_CREDENTIALS["password"])
            driver.find_element(By.NAME, "login").click()
            WebDriverWait(driver, config.TIMEOUT).until(EC.url_changes(config.LOGIN_URL))
            logger.info("Login successful")
        except Exception as e:
            logger.error(f"Login failed: {e}")
            raise

def process_page(driver, config):
    """Process a single page (main table + detailed pages)"""
    try:
        pages = config.PAGES
        for page in pages:
            logger.info(f"Processing page {page.get('url')}")
            if page.get("url") == "https://www.en-system.net/admin/tool/group_main.php":
                group = groups.Groups(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                group.process_page_groups()
            elif page.get("url") == "https://www.en-system.net/admin/tool/room_main.php":
                room = rooms.Rooms(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                room.process_page_rooms()
            elif page.get("url") == "https://www.en-system.net/admin/tool/age_main.php":
                age = ages.Ages(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                age.process_page_ages()
            elif page.get("url") == "https://www.en-system.net/admin/tool/course_main.php":
                course = courses.Courses(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                course.process_page_courses()
            elif page.get("url") == "https://www.en-system.net/admin/tool/short_course_main.php":
                short_course = short_courses.ShortCourses(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                short_course.process_page_short_courses()
            elif page.get("url") == "https://www.en-system.net/admin/tool/category_main.php":
                category = categories.Categories(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                category.process_page_categories()
            elif page.get("url") == "https://www.en-system.net/admin/tool/lesson_main.php":
                lesson = lessons.Lessons(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                lesson.process_page_lessons()
            elif page.get("url") == "https://www.en-system.net/admin/tool/shohin_cate_main.php":
                shohin_category = shohin_cate.ShohinCate(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                shohin_category.process_page_short_cate()
            elif page.get("url") == "https://www.en-system.net/admin/tool/shohin_vari_main.php":
                shohin_variable = shohin_vari.ShohinVari(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                shohin_variable.process_page_short_vari()
            elif page.get("url") == "https://www.en-system.net/admin/tool/schedule_teacher_main.php":
                point = point_search.PointSearch(driver, page.get("url"), page.get("output_dir"), page.get("buttons"))
                point.process_all_pages()
            # Extract main table data
    except Exception as e:
        logger.error(f"Error processing page {page.get('url')}: {e}")

def initialize_driver(driver):
    """Initialize the Selenium WebDriver with enhanced crash protection"""
    try:
        # Import the recovery manager for enhanced stability
        from services.point_search import WebDriverRecoveryManager

        # Use the recovery manager to create a stable driver
        recovery_manager = WebDriverRecoveryManager(max_retries=3, retry_delay=5)
        driver = recovery_manager.create_stable_driver()

        if driver:
            logger.info("WebDriver initialized successfully with crash protection")
            return driver
        else:
            logger.error("Failed to create WebDriver with recovery manager")
            return None

    except Exception as e:
        logger.error(f"Failed to initialize WebDriver: {e}")

        # Fallback to original method if recovery manager fails
        try:
            logger.info("Attempting fallback WebDriver initialization...")
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--ignore-ssl-errors")
            chrome_options.add_argument("--allow-insecure-localhost")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")

            driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
            driver.set_page_load_timeout(60)

            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            logger.info("Fallback WebDriver initialized successfully")
            return driver

        except Exception as fallback_error:
            logger.error(f"Fallback WebDriver initialization also failed: {fallback_error}")
            return None

def main():
    driver = None  # Ensure chromedriver is in PATH
    config = Config()
    try:

        # Initialize the driver
        driver = initialize_driver(driver)
        # Handle login if needed
        login(driver, config)
        # Process the page (add more URLs for multiple pages)
        process_page(driver, config)

    except Exception as e:
        logger.error(f"Main process error: {e}")
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    main()