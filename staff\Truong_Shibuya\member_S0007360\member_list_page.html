<html xmlns="http://www.w3.org/1999/xhtml" lang="ja" xml:lang="ja"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Style-Type" content="text/css">
    <meta http-equiv="Content-Script-Type" content="text/javascript">
    <title>EN-DANCE SYSTEM</title>
    <link href="css/import.css" rel="stylesheet" type="text/css" media="all">
    <link href="css/jquery-ui.css" rel="stylesheet" type="text/css" media="all">
    <script type="text/javascript" charset="UTF-8" src="https://www.en-system.net/staff/tool/js/input_check.js"></script>
    <script type="text/javascript" src="js/jquery-1.8.3.js"></script>
    <style type="text/css">
        .SideNavi li#sideNav6 a {
            background-color: #6D6B6A;color:#DADF00;
        }
    </style>
    <script language="JavaScript">
        function PdfCheck(parts, sir_id){
            parts.action='user_sign_pdf.php';
            parts.target = '_self';
            parts.sir_id.value = sir_id;
            parts.submit();
            return true;
        }

        function SearchCheck(parts) {
            year = parts.sr_st_date_year;
            month = parts.sr_st_date_month;
            day = parts.sr_st_date_day;
            if (refNum(year, month, day) == false) {
                parts.sr_st_date_year.focus();
                return false;
            }
            year = parts.sr_ed_date_year;
            month = parts.sr_ed_date_month;
            day = parts.sr_ed_date_day;
            if (refNum(year, month, day) == false) {
                parts.sr_ed_date_year.focus();
                return false;
            }
            parts.action='users_sign_main.php';
            parts.or_sort.value = '';
            parts.target = '_self';
            parts.submit();
            return true;
        }

        function refNum(year, month, day, parts) {
            if (year.value != '' && month.value != '' && day.value != '') {
                if (DateCheck_1(year.value + '-' + month.value + '-' + day.value) == false) {
                    alert("日付が正しくありません");
                    year.focus();
                    return false;
                }
            }
            return true;
        }
    </script>
</head>

<body>

<div id="wrapAll">

    <!-- heade.tpl -->

<!-- #topH1  -->
<div id="topH1">
<div class="boxHead">
<a href="./index.php"><img src="../images/logo.jpg" height="50"></a>
</div>
<p id="nameP">2025/05/22（木）</p>
<h2>スタッフ用ツール　　　
<select name="Studio" id="selStudios" onchange="document.move_room.room_id.value=this.options[this.selectedIndex].value;document.move_room.submit();">
  <option value="1" selected="">渋谷校</option>
  <option value="9">渋谷2nd校</option>
  <option value="10">渋谷SCRAMBLE</option>
  <option value="11">EnSTUDIO</option>
  <option value="2">横浜校</option>
</select>
　　平野さん</h2>

<div class="div_tab clearfix mb05">
<span class="btntab1">メインメニュー</span>
<span class="btntab2"><a href="rental_yoyaku_main.php">貸しスタジオ</a></span>
</div>

<ul>
  <li id="btnLogout"><a href="../logout.php">ログアウト</a></li>
</ul>

<form action="move_room.php" name="move_room" method="post">
<input type="hidden" name="room_id" value="">
<input type="hidden" name="login_mode" value="login">
</form>

</div>
<!--/ #topH1 -->

<!-- #glnavi -->
<!--/ #glnavi -->

<!-- /heade.tpl -->

    <div class="bread"></div>

    <div id="wrapMain" class="clearfix">

        <div id="content" class="clearfix">

            <h2><span>会員署名一覧</span></h2>

            <form action="users_sign_main.php" method="post" name="searchform">
                <div class="boxSearch2 clearfix mb10">
                    <dl class="flDL-1 ddL searchDL clearfix">
                        <dt>
                            <img width="18" height="18" alt="検索" src="images/icon-search.gif">
                        </dt>
                        <dd style="font-weight:bold;">基本検索</dd>
                        <dd class="clearfix">
                            会員番号：<input type="text" name="sr_code" class="txt-S" maxlength="200" value="">
                            　　会員氏名：<input type="text" name="sr_kanji" class="txt-S" maxlength="200" value="">
                            　　会員カナ：<input type="text" name="sr_kana" class="txt-S" maxlength="200" value="">
                            　　性別：
                            <select name="sr_sex">
                                <option value="">全て</option>
                                                                <option value="1">男</option>
                                <option value="2">女</option>
                            </select>
                            　　区分：
                            <select name="sr_kbn">
                                <option value="">全て</option>
                                                                <option value="1">退会</option>
                                                                <option value="2">コース変更</option>
                                                                <option value="3">休会</option>
                                                                <option value="4">ウォータースタンド解約</option>
                                                                <option value="5">在籍変更</option>
                                                            </select>
                            　　署名状態：
                            <select name="sr_status">
                                <option value="">全て</option>
                                                                <option value="1">未署名</option>
                                <option value="2">署名済</option>
                            </select>
                        </dd>
                        <dd class="clearfix">
                            署名日：
                            <select name="sr_st_date_year" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="2012">2012
</option><option value="2013">2013
</option><option value="2014">2014
</option><option value="2015">2015
</option><option value="2016">2016
</option><option value="2017">2017
</option><option value="2018">2018
</option><option value="2019">2019
</option><option value="2020">2020
</option><option value="2021">2021
</option><option value="2022">2022
</option><option value="2023">2023
</option><option value="2024">2024
</option><option value="2025">2025
</option><option value="2026">2026
</option></select>年<select name="sr_st_date_month" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option></select>月
<select name="sr_st_date_day" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option><option value="13">13
</option><option value="14">14
</option><option value="15">15
</option><option value="16">16
</option><option value="17">17
</option><option value="18">18
</option><option value="19">19
</option><option value="20">20
</option><option value="21">21
</option><option value="22">22
</option><option value="23">23
</option><option value="24">24
</option><option value="25">25
</option><option value="26">26
</option><option value="27">27
</option><option value="28">28
</option><option value="29">29
</option><option value="30">30
</option><option value="31">31
</option></select>日
                            ～
                            <select name="sr_ed_date_year" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="2012">2012
</option><option value="2013">2013
</option><option value="2014">2014
</option><option value="2015">2015
</option><option value="2016">2016
</option><option value="2017">2017
</option><option value="2018">2018
</option><option value="2019">2019
</option><option value="2020">2020
</option><option value="2021">2021
</option><option value="2022">2022
</option><option value="2023">2023
</option><option value="2024">2024
</option><option value="2025">2025
</option><option value="2026">2026
</option></select>年<select name="sr_ed_date_month" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option></select>月
<select name="sr_ed_date_day" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option><option value="13">13
</option><option value="14">14
</option><option value="15">15
</option><option value="16">16
</option><option value="17">17
</option><option value="18">18
</option><option value="19">19
</option><option value="20">20
</option><option value="21">21
</option><option value="22">22
</option><option value="23">23
</option><option value="24">24
</option><option value="25">25
</option><option value="26">26
</option><option value="27">27
</option><option value="28">28
</option><option value="29">29
</option><option value="30">30
</option><option value="31">31
</option></select>日
                        </dd>
                    </dl>
                    <ul class="flUl-2 mb05 clearfix">
                        <li class="txt">
                            <a href="users_sign_main.php">検索条件をクリア</a>
                        </li>
                        <li class="btnBluSB btnM">
                            <span>
                                <input type="button" name="search" value="検　索" onclick="SearchCheck(this.form);">
                            </span>
                        </li>
                    </ul>
                </div>
                <input type="hidden" name="or_sort" value="">
            </form>

            <form action="users_sign_main.php" method="post" name="sort_form">
                <input type="hidden" name="or_sort" value="">
                <input type="hidden" name="stpos" value="1">            </form>
        <div>

        <table summary="一覧" class="newsTbl">
            <colgroup class="td10"></colgroup>
            <colgroup class="td07"></colgroup>
            <colgroup class="td10"></colgroup>
            <colgroup class="td10"></colgroup>
            <colgroup class="td15"></colgroup>
            <colgroup class="td07"></colgroup>
            <colgroup class="td07"></colgroup>
            <tbody><tr>
                <th scope="col">
                    区分
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='1';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='2';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    会員番号
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='3';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='4';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    会員名
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='5';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='6';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    フリガナ
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='7';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='8';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    署名日
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='9';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='10';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">電子証明</th>
                <th scope="col"></th>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0007665</td>
                <td>辰野  新菜</td>
                <td>タツノ  ニイナ</td>
                <td>2025年05月21日 16時17分52秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4048);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4048; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009534</td>
                <td>川浪  萌奈</td>
                <td>カワナミ  モエナ</td>
                <td>2025年05月20日 21時47分32秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4047);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4047; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>S0008300</td>
                <td>清水  貴新</td>
                <td>シミズ  キアラ</td>
                <td>2025年05月20日 20時13分37秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4045);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4045; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0005043</td>
                <td>赤石澤  杏</td>
                <td>アカイシザワ  アン</td>
                <td>2025年05月20日 18時35分20秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4041);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4041; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0008027</td>
                <td>安田  桜</td>
                <td>ヤスダ  サクラ</td>
                <td>2025年05月20日 16時16分05秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4040);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4040; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009422</td>
                <td>秋田  唯妃</td>
                <td>アキタ  ユイ</td>
                <td>2025年05月17日 13時19分09秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4027);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4027; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0009422</td>
                <td>秋田  唯妃</td>
                <td>アキタ  ユイ</td>
                <td>2025年05月17日 12時54分54秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4025);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4025; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>在籍変更</td>
                <td>S0009422</td>
                <td>秋田  唯妃</td>
                <td>アキタ  ユイ</td>
                <td>2025年05月17日 12時51分18秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4024);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4024; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0008351</td>
                <td>平野  史華</td>
                <td>ヒラノ  フミカ</td>
                <td>2025年05月16日 19時13分18秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4015);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4015; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0007800</td>
                <td>風間  祐希</td>
                <td>カザマ  ユウキ</td>
                <td>2025年05月16日 18時55分39秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4013);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4013; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0007800</td>
                <td>風間  祐希</td>
                <td>カザマ  ユウキ</td>
                <td>2025年05月16日 18時53分43秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4012);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4012; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0008153</td>
                <td>西山  瑛奈</td>
                <td>ニシヤマ  エナ</td>
                <td>2025年05月16日 15時38分48秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4011);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4011; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0007908</td>
                <td>佐藤  彩香</td>
                <td>サトウ  アヤカ</td>
                <td>2025年05月15日 21時09分36秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4009);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4009; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時50分33秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4008);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4008; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時49分56秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4007);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4007; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時49分20秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4006);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4006; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時48分50秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4005);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4005; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時48分20秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4004);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4004; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時47分43秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4003);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4003; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>休会</td>
                <td>S0006327</td>
                <td>寺崎  裕希</td>
                <td>テラサキ  ユウキ</td>
                <td>2025年05月15日 18時46分48秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4002);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4002; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0005945</td>
                <td>橋本  穂乃佳</td>
                <td>ハシモト  ホノカ</td>
                <td>2025年05月15日 18時34分35秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4001);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4001; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009430</td>
                <td>陳  諾</td>
                <td>チン  ダク</td>
                <td>2025年05月15日 17時46分38秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 4000);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=4000; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0007244</td>
                <td>坂下  愛実</td>
                <td>サカシタ  アミ</td>
                <td>2025年05月15日 15時28分05秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3996);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3996; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0006378</td>
                <td>中村  鈴音</td>
                <td>ナカムラ  スズネ</td>
                <td>2025年05月14日 17時42分14秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3992);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3992; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>S0008324</td>
                <td>佐藤  晴輝</td>
                <td>サトウ  ハルキ</td>
                <td>2025年05月13日 17時05分25秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3987);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3987; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009438</td>
                <td>池島  里咲子</td>
                <td>イケシマ  リサコ</td>
                <td>2025年05月13日 16時45分11秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3986);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3986; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>S0007643</td>
                <td>Kanno  Yuko</td>
                <td>カンノ  ユウコ</td>
                <td>2025年05月10日 21時07分06秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3974);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3974; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0007919</td>
                <td>孫  雅瞳</td>
                <td>ソン  アトウ</td>
                <td>2025年05月10日 19時37分43秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3971);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3971; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>S0008275</td>
                <td>石川  さくら</td>
                <td>イシカワ  サクラ</td>
                <td>2025年05月10日 18時59分21秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3970);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3970; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009468</td>
                <td>橘  彪吾</td>
                <td>タチバナ  ヒュウゴ</td>
                <td>2025年05月10日 18時23分25秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3968);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3968; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0009464</td>
                <td>富樫  涼太</td>
                <td>トガシ  リョウタ</td>
                <td>2025年05月10日 18時00分06秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3964);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3964; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0008226</td>
                <td>澤見  柊音</td>
                <td>サワミ  シオン</td>
                <td>2025年05月10日 17時49分58秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3963);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3963; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>S0006380</td>
                <td>山口  蒼那</td>
                <td>ヤマグチ  アオナ</td>
                <td>2025年05月10日 17時09分08秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3960);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3960; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009458</td>
                <td>杉田  ウィアフェ南奈</td>
                <td>スギタ  ウィアフェナナ</td>
                <td>2025年05月10日 15時41分10秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3955);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3955; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>S0007484</td>
                <td>外山  莉佳子</td>
                <td>トヤマ  リカコ</td>
                <td>2025年05月10日 15時19分40秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3952);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3952; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0007484</td>
                <td>外山  莉佳子</td>
                <td>トヤマ  リカコ</td>
                <td>2025年05月10日 15時17分44秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3951);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3951; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>S0008156</td>
                <td>西田  佳織</td>
                <td>ニシダ  カオリ</td>
                <td>2025年05月10日 12時36分51秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3942);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3942; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009453</td>
                <td>渡邊  翼</td>
                <td>ワタナベ  ツバサ</td>
                <td>2025年05月09日 19時53分03秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3936);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3936; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0009558</td>
                <td>宮下  柚音</td>
                <td>ミヤシタ  ユウネ</td>
                <td>2025年05月09日 18時39分55秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3934);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3934; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0007999</td>
                <td>黄  詩雲</td>
                <td>コウ  シユン</td>
                <td>2025年05月09日 17時49分45秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3933);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3933; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0008031</td>
                <td>福田  優晄</td>
                <td>フクダ  ヒナタ</td>
                <td>2025年05月09日 17時46分31秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3932);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3932; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0008301</td>
                <td>浅賀  優希</td>
                <td>アサカ  ユウキ</td>
                <td>2025年05月09日 15時44分47秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3927);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3927; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0009459</td>
                <td>村上  ほの</td>
                <td>ムラカミ  ホノ</td>
                <td>2025年05月09日 15時05分33秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3925);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3925; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>S0008333</td>
                <td>髙城  樹里杏</td>
                <td>タカギ  ジュリア</td>
                <td>2025年05月09日 15時04分18秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3924);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3924; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0009435</td>
                <td>中島  利奈</td>
                <td>ナカジマ  リナ</td>
                <td>2025年05月09日 15時00分24秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3923);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3923; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0006396</td>
                <td>むらん  じゅりあん</td>
                <td>ムラン  ジュリアン</td>
                <td>2025年05月08日 21時19分17秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3919);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3919; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0008084</td>
                <td>大高  希実</td>
                <td>オオタカ  ノゾミ</td>
                <td>2025年05月08日 20時49分52秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3917);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3917; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0009418</td>
                <td>小池  陽</td>
                <td>コイケ  ヒナタ</td>
                <td>2025年05月08日 20時42分27秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3916);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3916; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>S0008112</td>
                <td>中塚  香帆</td>
                <td>ナカヅカ  カホ</td>
                <td>2025年05月08日 20時30分14秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3915);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3915; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>S0008159</td>
                <td>西田  拓海</td>
                <td>ニシダ  タクミ</td>
                <td>2025年05月08日 20時01分14秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3911);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3911; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                    </tbody></table>

        <form action="users_sign_conf.php" method="post" name="edit_form">
            <input type="hidden" name="sir_id" value="">
            <input type="hidden" name="or_sort" value="">
            <input type="hidden" name="stpos" value="1">        </form>

        <form action="" method="post" name="next_page">
            <input type="hidden" name="stpos" value="1">              <table border="0" cellspacing="0" cellpadding="2" width="700" class="px12">
    <tbody><tr>
      <td width="150">1220 件中　1 - 50 件</td>
      <td width="550">
Page: <span class="px14"><b>1</b></span> 
<a href="javascript:SetStartNo2(51);">2</a> 
<a href="javascript:SetStartNo2(101);">3</a> 
<a href="javascript:SetStartNo2(151);">4</a> 
<a href="javascript:SetStartNo2(201);">5</a> 
<a href="javascript:SetStartNo2(251);">6</a> 
<a href="javascript:SetStartNo2(301);">7</a> 
<a href="javascript:SetStartNo2(351);">8</a> 
<a href="javascript:SetStartNo2(401);">9</a> 
<a href="javascript:SetStartNo2(451);">10</a> 
<input type="submit" value=">>" onclick="return SetStartNo( this.form , 51 );" class="btn_nosize">
      </td>
    </tr>
  </tbody></table>
  <script language="JavaScript">
  <!--
  function SetStartNo(parts,no) {
       parts.stpos.value = no;
       return true;
  }
  function SetStartNo2(no) {
       document.next_page.stpos.value = no;
       document.next_page.submit();
  }
  //-->
  </script>
            <input type="hidden" name="or_sort" value="">
        </form>

        <form action="" name="pdf_form" method="post" enctype="multipart/form-data">
            <input type="hidden" name="sir_id" value="">
        </form>
    </div>

    <div class="gotop">
        <p><a href="#">ページトップへ</a></p>
    </div>
</div>

<div id="sideLeft">
    <!-- left_menu.tpl -->
<h3>会員管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav01" class="btm"><a href="nyukai_main.php">入会申込み</a></li>
    <li id="sideNav1" class="btm"><a href="user_mnt.php">会員新規登録</a></li>
    <li id="sideNav2" class="btm"><a href="user_main.php">会員一覧</a></li>
    <li id="sideNav3" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='1';document.frm_cardread.submit();">会員カードNo登録</a></li>
    <li id="sideNav4" class="btm"><a href="mail_haishin.php">会員メール配信</a></li>
    <li id="sideNav6" class="btm"><a href="users_sign_main.php">会員署名一覧</a></li>
  </ul>
</div>
<h3>レッスン管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav30" class="btm"><a href="jyuko_search.php">レッスン受講一覧</a></li>
    <li id="sideNav31" class="btm"><a href="taiken_search.php">体験レッスン管理</a></li>
    <li id="sideNav32" class="btm"><a href="user_oshirase_main.php">My Pageお知らせ登録</a></li>
  </ul>
</div>
<h3>月謝管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav11" class="btm"><a href="course_ichiran.php">受講可能コース一覧</a></li>
    <li id="sideNav10" class="btm"><a href="gessya_search.php">会員月謝一覧</a></li>
  </ul>
</div>
<h3>販売管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav40" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='2';document.frm_cardread.submit();">商品販売</a></li>
    <li id="sideNav43" class="btm"><a href="pos_search.php">販売履歴一覧</a></li>
    <li id="sideNav44" class="btm"><a href="kinsyu_hyo.php">金種表</a></li>
    <li id="sideNav42" class="btm"><a href="pos_settei.php">レジ設定</a></li>
    <li id="sideNav41" class="btm"><a href="shohin_add_mnt.php">商品登録</a></li>
  </ul>
</div>
<h3>売上集計</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav60" class="btm"><a href="syukei_uriage.php">全体売上集計</a></li>
    <li id="sideNav61" class="btm"><a href="syukei_course.php">コース別売上集計</a></li>
    <li id="sideNav62" class="btm"><a href="syukei_shohin.php">商品別売上集計</a></li>
    <li id="sideNav65" class="btm"><a href="syukei_studio.php">スタジオ収支進捗表</a></li>
    <li id="sideNav68" class="btm"><a href="studio_transition.php">スタジオ推移表</a></li>
    <li id="sideNav66" class="btm"><a href="syukei_uriage_yoso.php">売上要素分解表</a></li>
  </ul>
</div>
<h3>日報掲示板登録</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav70" class="btm"><a href="nippo_room.php">教室日報</a></li>
    <li id="sideNav71" class="btm"><a href="nippo_staff.php">スタッフ日報</a></li>
    <!-- <li id="sideNav72" class="btm"><a href="keijiban_main.php">掲示板</a></li> -->
    <li id="sideNav73" class="btm"><a href="nippo_front.php">フロント業務日報</a></li>
  </ul>
</div>
<h3>管理機能</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav20" class="btm"><a href="getsuji_kakutei.php">月次確定処理</a></li>
	<li id="sideNav23" class="btm"><a href="point_search.php">付与ポイント管理</a></li>
    <li id="sideNav21" class="btm"><a href="holiday_main.php">休日カレンダー</a></li>
    <li id="sideNav22" class="btm"><a href="minochk_hiduke.php">未納ﾁｪｯｸ開始日設定</a></li>
  </ul>
</div>
<form action="card_write.php" name="frm_cardread" method="post" target="_self">
<input type="hidden" name="mode" value="">
<input type="hidden" name="page_mode" value="">
</form>
<!-- /left_menu.tpl -->
</div>

<!-- footer.tpl -->
<div id="footer-space"></div><!-- footer固定用スペース -->
</div>
<!--/ #wrapAll -->

<!-- footer -->
<div id="wrapFoot">
<div id="footer">
</div>
</div>
<!--/ footer -->
<!-- /footer.tpl -->



</div></body></html>