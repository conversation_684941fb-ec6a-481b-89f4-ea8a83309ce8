import logging
import os
import base64
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebD<PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import json

class   ExtractPage:
    def __init__(self, driver, timeout=10):
        self.driver = driver
        self.wait = WebDriverWait(driver, timeout)
        self.logger = None
        self.save_screenshots = True  # Configurable flag
        self.debug_mode = True  # Configurable flag
        self.setup_logger()

    def setup_logger(self):
        """Set up logger"""
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(formatter)
        self.logger.addHandler(stream_handler)

    def extract_form_data(self):
        """Extract data from forms or tables on the page"""
        form_data = {}
        try:
            self.logger.info(f"Extracting form data from {self.driver.current_url}")

            # Form selectors to try
            form_selectors = [
                (By.ID, "mntform"),
                (By.NAME, "edit_form"),
                (By.NAME, "course_form"),
                (By.NAME, "short_course_form"),
                (By.TAG_NAME, "form")
            ]

            form = None
            for by, selector in form_selectors:
                try:
                    form = self.wait.until(EC.presence_of_element_located((by, selector)))
                    self.logger.info(f"Found form with selector: {selector}")
                    break
                except:
                    continue

            if form:
                # Extract inputs, selects, textareas
                inputs = form.find_elements(By.TAG_NAME, "input")
                selects = form.find_elements(By.TAG_NAME, "select")
                textareas = form.find_elements(By.TAG_NAME, "textarea")

                for input_field in inputs:
                    try:
                        field_type = input_field.get_attribute("type")
                        name = input_field.get_attribute("name") or input_field.get_attribute("id")
                        if name and field_type not in ['submit', 'button', 'reset']:
                            form_data[name] = input_field.get_attribute("value") or ""
                    except:
                        continue

                for select in selects:
                    try:
                        name = select.get_attribute("name") or select.get_attribute("id")
                        if name:
                            selected_option = select.find_element(By.CSS_SELECTOR, "option[selected]")
                            form_data[name] = selected_option.get_attribute("value") or ""
                    except:
                        continue

                for textarea in textareas:
                    try:
                        name = textarea.get_attribute("name") or textarea.get_attribute("id")
                        if name:
                            form_data[name] = textarea.get_attribute("value") or textarea.text
                    except:
                        continue
                
                for key in form_data:
                    self.logger.info(f"Extracted form data")
                    self.logger.info(f"{key}: {form_data[key]}")
                    # Remove this line as it uses undefined variables
                    # self.logger.info(f"{data[name]: {form_data[data]}}")


            # Extract table data
            # form_data["tables"] = self.extract_table_data()

            # Save form data to JSON file
  
            # os.makedirs(output_dir, exist_ok=True)
            # timestamp = self.driver.current_url.split("gr_id=")[-1] if "gr_id=" in self.driver.current_url else "default"
            # json_path = os.path.join(output_dir, filename)
            # with open(json_path, "w", encoding="utf-8") as f:
            #     json.dump(form_data, f, ensure_ascii=False, indent=2)
            # self.logger.info(f"Saved form data to {json_path}")

            # # Save screenshot
            # screenshot_path = os.path.join(output_dir, f"screenshot_{timestamp}.png")
            # self.save_screenshot(output_dir, f"screenshot_{timestamp}.png")
            # self.logger.info(f"Saved screenshot to {screenshot_path}")

            # # Save HTML source
            # html_path = os.path.join(output_dir, f"page_{timestamp}.html")
            # self.save_page_source(output_dir, f"page_{timestamp}.html")
            # self.logger.info(f"Saved HTML source to {html_path}")

            return form_data

        except Exception as e:
            self.logger.error(f"Error extracting form data: {e}")
            return form_data

    def extract_table_data(self):
        """Extract data from all tables on the page"""
        try:
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            if not tables:
                self.logger.info("Không tìm thấy bảng nào trên trang")
                return []

            self.logger.info(f"Tìm thấy {len(tables)} bảng trên trang")
            table_data = []
            for idx, table in enumerate(tables):
                self.logger.info(f"Đang xử lý bảng thứ {idx + 1}")
                headers = [th.text.strip() for th in table.find_elements(By.TAG_NAME, "th")]
                if headers:
                    self.logger.info(f"Các cột trong bảng: {', '.join(headers)}")
                else:
                    self.logger.info("Bảng không có tiêu đề cột")

                rows = []
                for row_idx, row in enumerate(table.find_elements(By.TAG_NAME, "tr")[1:]):  # Skip header row
                    cells = [td.text.strip() for td in row.find_elements(By.TAG_NAME, "td")]
                    if cells:
                        row_data = dict(zip(headers, cells)) if headers else cells
                        self.logger.info(f"Dữ liệu dòng {row_idx + 1}")
                        for data in row_data:
                            self.logger.info(f"{data}: {row_data[data]}")
                        rows.append(row_data)
                
                table_info = {"headers": headers, "rows": rows}
                self.logger.info(f"Tổng số dòng trong bảng {idx + 1}: {len(rows)}")
                table_data.append(table_info)
            
            return table_data
        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất dữ liệu bảng: {e}")
            return []

    def save_screenshot(self, directory, filename):
        """Save a screenshot to the specified directory"""
        if not self.save_screenshots and not self.debug_mode:
            self.logger.warning("Screenshots are disabled. Skipping save.")
            return None

        try:
            os.makedirs(directory, exist_ok=True)
            screenshot_path = os.path.join(directory, filename)

            page_metrics = self.driver.execute_cdp_cmd('Page.getLayoutMetrics', {})
            screenshot_config = {
                'captureBeyondViewport': True,
                'fromSurface': True,
                'clip': {
                    'width': page_metrics['contentSize']['width'],
                    'height': page_metrics['contentSize']['height'],
                    'x': 0,
                    'y': 0,
                    'scale': 1
                }
            }

            result = self.driver.execute_cdp_cmd('Page.captureScreenshot', screenshot_config)
            with open(screenshot_path, 'wb') as f:
                f.write(base64.b64decode(result['data']))

            if self.debug_mode:
                self.logger.debug(f"Saved screenshot to {screenshot_path}")
            return screenshot_path
        except Exception as e:
            self.logger.error(f"Error capturing screenshot: {e}")
            return None

    def save_page_source(self, directory, filename):
        """Save the page source to the specified directory"""
        try:
            os.makedirs(directory, exist_ok=True)
            html_path = os.path.join(directory, filename)
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            self.logger.debug(f"Saved page source to {html_path}")
            return html_path
        except Exception as e:
            self.logger.error(f"Error saving page source: {e}")
            return None
    
    def extract_member_image(self, content, button_dir, id):
        """Extract all images from the page and member profile image"""
        self.logger.info(f"Extracting images for member {id}")
        try:
            # Create images directory
            images_dir = os.path.join(button_dir, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            # Tìm tất cả các thẻ img trên trang
            images = content.find_elements(By.TAG_NAME, "img")
            self.logger.info(f"Found {len(images)} images on the page")
            
            image_data = []
            for idx, img in enumerate(images):
                try:
                    # Lấy các thuộc tính của ảnh
                    src = img.get_attribute("src")
                    alt = img.get_attribute("alt") or f"image_{idx}"
                    width = img.get_attribute("width")
                    height = img.get_attribute("height")
                    
                    if not src:
                        continue
                        
                    # Lưu thông tin ảnh
                    image_info = {
                        "src": src,
                        "alt": alt,
                        "width": width,
                        "height": height
                    }
                    
                    # Tải ảnh nếu là URL hợp lệ
                    if src.startswith(("http://", "https://")):
                        try:
                            import requests
                            from requests.packages.urllib3.exceptions import InsecureRequestWarning
                            requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
                            
                            # Lấy cookies từ trình duyệt
                            cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
                            
                            # Tải ảnh
                            response = requests.get(src, cookies=cookies, verify=False, timeout=10)
                            
                            if response.status_code == 200 and len(response.content) > 1000:
                                # Tạo tên file từ alt text hoặc index
                                filename = f"{id}_{alt}_{idx}.jpg"
                                filename = "".join(c for c in filename if c.isalnum() or c in "._- ")
                                img_path = os.path.join(images_dir, filename)
                                
                                # Lưu ảnh
                                with open(img_path, 'wb') as f:
                                    f.write(response.content)
                                
                                image_info["saved_path"] = img_path
                                self.logger.info(f"Downloaded image to {img_path}")
                        except Exception as e:
                            self.logger.warning(f"Failed to download image {src}: {str(e)}")
                    image_data.append(image_info)
                    
                except Exception as e:
                    self.logger.warning(f"Error processing image {idx}: {str(e)}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Failed to extract member image", e)
            